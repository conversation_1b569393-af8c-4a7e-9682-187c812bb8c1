import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Link } from "react-router-dom";
import {
  selectMyDownloads,
  selectMyRequests,
  selectMyBids,
  selectDashboardStats,
  selectLoading,
  selectErrors,
  fetchBuyerDashboardStats,
  fetchBuyerDownloads,
  fetchBuyerRequests,
  fetchBuyerBids,
  clearError,
} from "../../redux/slices/buyerDashboardSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import Table from "../../components/common/Table";
import LoadingSkeleton, { DashboardSkeleton, StatCardSkeleton } from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { FaEye, FaSync } from "react-icons/fa";
import "../../styles/BuyerAccountDashboard.css";
import { MdDashboard } from "react-icons/md";
import { FaDownload } from "react-icons/fa";
import { FaGavel } from "react-icons/fa";
import { MdRequestPage } from "react-icons/md";

const BuyerAccountDashboard = () => {
  const dispatch = useDispatch();
  const downloads = useSelector(selectMyDownloads);
  const requests = useSelector(selectMyRequests);
  const bids = useSelector(selectMyBids);
  const stats = useSelector(selectDashboardStats);
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);



  // Fetch data on component mount
  useEffect(() => {
    dispatch(fetchBuyerDashboardStats());
    dispatch(fetchBuyerDownloads({ limit: 2 }));
    dispatch(fetchBuyerRequests({ limit: 2 }));
    dispatch(fetchBuyerBids({ limit: 2 }));
  }, [dispatch]);

  // Handle retry for specific sections
  const handleRetry = (section) => {
    dispatch(clearError(section));
    switch (section) {
      case 'stats':
        dispatch(fetchBuyerDashboardStats());
        break;
      case 'downloads':
        dispatch(fetchBuyerDownloads({ limit: 2 }));
        break;
      case 'requests':
        dispatch(fetchBuyerRequests({ limit: 2 }));
        break;
      case 'bids':
        dispatch(fetchBuyerBids({ limit: 2 }));
        break;
      default:
        break;
    }
  };

  // Show loading skeleton for initial load
  if (loading.stats && loading.downloads && loading.requests && loading.bids) {
    return (
      <div className="BuyerAccountDashboard">
        <SectionWrapper
          icon={<MdDashboard className="BuyerSidebar__icon" />}
          title="Dashboard"
        >
          <DashboardSkeleton />
        </SectionWrapper>
      </div>
    );
  }

  // Column definitions for Downloads table
  const downloadsColumns = [
    { key: "no", label: "No.", className: "no" },
    { key: "order-id", label: "Order Id", className: "order-id" },
    { key: "video", label: "Videos/Documents", className: "video" },
    { key: "date", label: "Date", className: "date" },
    { key: "amount", label: "Amount", className: "amount" },
    { key: "status", label: "Status", className: "status" },
    { key: "action", label: "Action", className: "action" },
  ];

  // Column definitions for Requests table
  const requestsColumns = [
    { key: "no", label: "No.", className: "no" },
    { key: "order-id", label: "Order Id", className: "order-id" },
    { key: "video", label: "Videos/Documents", className: "video" },
    { key: "date", label: "Date", className: "date" },
    { key: "amount", label: "Requested Amount", className: "amount" },
    { key: "status", label: "Status", className: "status" },
    { key: "action", label: "Action", className: "action" },
  ];

  // Column definitions for Bids table
  const bidsColumns = [
    { key: "no", label: "No.", className: "no" },
    { key: "order-id", label: "Bid Id", className: "order-id" },
    { key: "video", label: "Videos/Documents", className: "video" },
    { key: "date", label: "Date", className: "date" },
    { key: "amount", label: "Bid Amount", className: "amount" },
    { key: "status", label: "Status", className: "status" },
    { key: "action", label: "Action", className: "action" },
  ];

  // Render function for Downloads rows
  const renderDownloadsRow = (download, index) => (
    <>
      <div className="table-cell no">{index + 1}</div>
      <div className="table-cell order-id">#{download.orderId?.slice(-8) || download.id}</div>
      <div className="table-cell video">
        <div className="content-item">
          <div className="content-image">
            <img
              src={download.thumbnail || "https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop"}
              alt={download.title}
            />
          </div>
          <div className="content-info">
            <div className="content-title">{download.title}</div>
            <div className="content-coach">By {download.coach}</div>
          </div>
        </div>
      </div>
      <div className="table-cell date">{download.downloadDate}</div>
      <div className="table-cell amount">
        ${download.amount?.toFixed(2) || '0.00'}
      </div>
      <div className="table-cell status">
        <span className="status-badge downloaded">Downloaded</span>
      </div>
      <div className="table-cell action">
        <button className="action-btn">
          <FaEye />
        </button>
      </div>
    </>
  );

  // Render function for Requests rows
  const renderRequestsRow = (request, index) => (
    <>
      <div className="table-cell no">{index + 1}</div>
      <div className="table-cell order-id">#{request.id}</div>
      <div className="table-cell video">
        <div className="content-item">
          <div className="content-image">
            <img
              src="https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop"
              alt={request.title}
            />
          </div>
          <div className="content-info">
            <div className="content-title">{request.title}</div>
            <div className="content-coach">By Coach</div>
          </div>
        </div>
      </div>
      <div className="table-cell date">{request.date} | 4:30PM</div>
      <div className="table-cell amount">
        ${(Math.random() * 30 + 20).toFixed(2)}
      </div>
      <div className="table-cell status">
        <span className={`status-badge ${request.status}`}>
          {request.status}
        </span>
      </div>
      <div className="table-cell action">
        <button className="action-btn">
          <FaEye />
        </button>
      </div>
    </>
  );

  // Render function for Bids rows
  const renderBidsRow = (bid, index) => (
    <>
      <div className="table-cell no">{index + 1}</div>
      <div className="table-cell order-id">#{bid.id}</div>
      <div className="table-cell video">
        <div className="content-item">
          <div className="content-image">
            <img
              src="https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop"
              alt={bid.title}
            />
          </div>
          <div className="content-info">
            <div className="content-title">{bid.title}</div>
            <div className="content-coach">By {bid.coach}</div>
          </div>
        </div>
      </div>
      <div className="table-cell date">{bid.date} | 4:30PM</div>
      <div className="table-cell amount">${bid.bidAmount.toFixed(2)}</div>
      <div className="table-cell status">
        <span className={`status-badge ${bid.status}`}>{bid.status}</span>
      </div>
      <div className="table-cell action">
        <button className="action-btn">
          <FaEye />
        </button>
      </div>
    </>
  );

  return (
    <div className="BuyerAccountDashboard">
      <SectionWrapper
        icon={<MdDashboard className="BuyerSidebar__icon" />}
        title="Dashboard"
      >
        {/* Stats Cards */}
        {errors.stats ? (
          <ErrorDisplay
            error={errors.stats}
            onRetry={() => handleRetry('stats')}
            title="Failed to load dashboard stats"
            className="stats-error"
          />
        ) : (
          <div className="stats">
            {loading.stats ? (
              <>
                <StatCardSkeleton />
                <StatCardSkeleton />
                <StatCardSkeleton />
              </>
            ) : (
              <>
                <div className="stat-card downloads">
                  <div className="stat-number">
                    {(stats.totalDownloads || downloads.length).toString().padStart(2, "0")}
                    <div className="stat-label">Total Downloads</div>
                  </div>
                  <div className="icon-round"><FaDownload /></div>
                </div>

                <div className="stat-card requests">
                  <div className="stat-number">
                    {(stats.totalRequests || requests.length).toString().padStart(2, "0")}
                    <div className="stat-label">Total Requests</div>
                  </div>
                  <div className="icon-roundtwo"><MdRequestPage /></div>
                </div>

                <div className="stat-card bids">
                  <div className="stat-number">
                    {(stats.totalBids || bids.length).toString().padStart(2, "0")}
                    <div className="stat-label">Total Bids</div>
                  </div>
                  <div className="icon-roundthree"><FaGavel /></div>
                </div>
              </>
            )}
          </div>
        )}

        {/* My Downloads Section */}
        <div className="section">
          <div className="section-header">
            <h3 className="section-title">My Downloads</h3>
            <div className="section-actions">
              <Link to="/buyer/account/downloads" className="view-all">
                View All Downloads
              </Link>
              {errors.downloads && (
                <button
                  className="retry-btn"
                  onClick={() => handleRetry('downloads')}
                  title="Retry loading downloads"
                >
                  <FaSync />
                </button>
              )}
            </div>
          </div>

          {errors.downloads ? (
            <ErrorDisplay
              error={errors.downloads}
              onRetry={() => handleRetry('downloads')}
              title="Failed to load downloads"
            />
          ) : loading.downloads ? (
            <LoadingSkeleton count={2} height="60px" className="table-row-skeleton" />
          ) : (
            <Table
              columns={downloadsColumns}
              data={downloads.slice(0, 2)}
              renderRow={renderDownloadsRow}
              variant="grid"
              gridTemplate="0.5fr 1fr 3fr 1.5fr 1fr 1fr 0.5fr"
              className="BuyerAccountDashboard__downloads-table"
              emptyMessage="No downloads yet."
            />
          )}
        </div>

        {/* My Requests Section */}
        <div className="section">
          <div className="section-header">
            <h3 className="section-title">My Requests</h3>
            <div className="section-actions">
              <Link to="/buyer/account/requests" className="view-all">
                View All Requests
              </Link>
              {errors.requests && (
                <button
                  className="retry-btn"
                  onClick={() => handleRetry('requests')}
                  title="Retry loading requests"
                >
                  <FaSync />
                </button>
              )}
            </div>
          </div>

          {errors.requests ? (
            <ErrorDisplay
              error={errors.requests}
              onRetry={() => handleRetry('requests')}
              title="Failed to load requests"
            />
          ) : loading.requests ? (
            <LoadingSkeleton count={2} height="60px" className="table-row-skeleton" />
          ) : (
            <Table
              columns={requestsColumns}
              data={Array.isArray(requests) ? requests.slice(0, 2) : []}
              renderRow={renderRequestsRow}
              variant="grid"
              gridTemplate="0.5fr 1fr 3fr 1.5fr 1.5fr 1fr 0.5fr"
              className="BuyerAccountDashboard__requests-table"
              emptyMessage="No requests yet."
            />
          )}
        </div>

        {/* My Bids Section */}
        <div className="section">
          <div className="section-header">
            <h3 className="section-title">My Bids</h3>
            <div className="section-actions">
              <Link to="/buyer/account/bids" className="view-all">
                View All Bids
              </Link>
              {errors.bids && (
                <button
                  className="retry-btn"
                  onClick={() => handleRetry('bids')}
                  title="Retry loading bids"
                >
                  <FaSync />
                </button>
              )}
            </div>
          </div>

          {errors.bids ? (
            <ErrorDisplay
              error={errors.bids}
              onRetry={() => handleRetry('bids')}
              title="Failed to load bids"
            />
          ) : loading.bids ? (
            <LoadingSkeleton count={2} height="60px" className="table-row-skeleton" />
          ) : (
            <Table
              columns={bidsColumns}
              data={Array.isArray(bids) ? bids.slice(0, 2) : []}
              renderRow={renderBidsRow}
              variant="grid"
              gridTemplate="0.5fr 1fr 3fr 1.5fr 1fr 1fr 0.5fr"
              className="BuyerAccountDashboard__bids-table"
              emptyMessage="No bids yet."
            />
          )}
        </div>
      </SectionWrapper>
    </div>
  );
};

export default BuyerAccountDashboard;
