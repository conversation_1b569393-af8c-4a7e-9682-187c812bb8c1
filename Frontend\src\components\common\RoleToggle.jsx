import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toggleRole } from '../../redux/slices/authSlice';
import toast from '../../utils/toast';
import '../../styles/RoleToggle.css';

const RoleToggle = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { user, isLoading } = useSelector((state) => state.auth);
  const [isToggling, setIsToggling] = useState(false);

  // Don't render if user is admin or not authenticated
  if (!user || user.role === 'admin') {
    return null;
  }

  const currentActiveRole = user.activeRole || user.role;
  const isBuyerMode = currentActiveRole === 'buyer';

  const handleToggle = async () => {
    if (isToggling || isLoading) return;

    setIsToggling(true);
    try {
      const result = await dispatch(toggleRole()).unwrap();
      
      // Show success message
      toast.success(`Switched to ${result.data.activeRole} mode`);
      
      // Navigate to appropriate dashboard
      if (result.data.activeRole === 'buyer') {
        navigate('/buyer/dashboard');
      } else {
        navigate('/seller/dashboard');
      }
    } catch (error) {
      toast.error(error.message || 'Failed to switch role');
    } finally {
      setIsToggling(false);
    }
  };

  return (
    <div className="role-toggle">
      <div className="role-toggle__container">
        <span className="role-toggle__label">
          {isBuyerMode ? 'Buyer Mode' : 'Seller Mode'}
        </span>
        <button
          className={`role-toggle__switch ${isBuyerMode ? 'buyer' : 'seller'}`}
          onClick={handleToggle}
          disabled={isToggling || isLoading}
          title={`Switch to ${isBuyerMode ? 'Seller' : 'Buyer'} mode`}
        >
          <div className="role-toggle__slider">
            <div className="role-toggle__thumb">
              {isBuyerMode ? 'B' : 'S'}
            </div>
          </div>
          <span className="role-toggle__text">
            {isToggling ? 'Switching...' : `Switch to ${isBuyerMode ? 'Seller' : 'Buyer'}`}
          </span>
        </button>
      </div>
    </div>
  );
};

export default RoleToggle;
