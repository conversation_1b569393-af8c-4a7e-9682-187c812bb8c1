// import React, { useEffect } from 'react';
// import { Navigate, useLocation } from 'react-router-dom';
// import { useSelector } from 'react-redux';
// import authService from '../../services/authService';
// import { needsSellerOnboarding, requiresOnboardingComplete } from '../../utils/sellerUtils';
// import { isRouteAuthorizedForUser, handleUnauthorizedAccess } from '../../utils/navigationUtils';
// import { useNavigate } from 'react-router-dom';

// /**
//  * ProtectedRoute component that prevents access based on authentication status
//  * @param {Object} props - Component props
//  * @param {React.ReactNode} props.children - Child components to render
//  * @param {boolean} props.requireAuth - If true, requires authentication to access
//  * @param {boolean} props.preventAuth - If true, prevents authenticated users from accessing
//  * @param {string} props.redirectTo - Path to redirect to when access is denied
//  * @param {string|Array} props.allowedRoles - Roles allowed to access this route
//  */
// const ProtectedRoute = ({
//   children,
//   requireAuth = false,
//   preventAuth = false,
//   redirectTo = '/',
//   allowedRoles = null
// }) => {
//   const { user, isAuthenticated } = useSelector((state) => state.auth);
//   const location = useLocation();
//   const navigate = useNavigate();

//   // Check if user is authenticated (from Redux or localStorage)
//   const isUserAuthenticated = isAuthenticated || authService.isAuthenticated();
//   const userData = user || authService.getStoredUser();

//   // If route prevents authenticated users and user is authenticated, redirect
//   if (preventAuth && isUserAuthenticated) {
//     // Redirect based on user role and onboarding status
//     if (userData?.role === 'seller') {
//       // For sellers, check onboarding status
//       if (needsSellerOnboarding(userData)) {
//         return <Navigate to="/seller-onboarding" replace />;
//       }
//       return <Navigate to="/seller/dashboard" replace />;
//     } else if (userData?.role === 'admin') {
//       return <Navigate to="/admin/dashboard" replace />;
//     } else {
//       return <Navigate to="/buyer/dashboard" replace />;
//     }
//   }

//   // If route requires authentication and user is not authenticated, redirect
//   if (requireAuth && !isUserAuthenticated) {
//     return <Navigate to="/auth" replace />;
//   }

//   // If specific roles are required, check user role
//   if (allowedRoles && userData) {
//     const userRole = userData.role;
//     const rolesArray = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];

//     if (!rolesArray.includes(userRole)) {
//       // Redirect to appropriate dashboard based on user's actual role
//       if (userRole === 'seller') {
//         return <Navigate to="/seller/dashboard" replace />;
//       } else if (userRole === 'admin') {
//         return <Navigate to="/admin/dashboard" replace />;
//       } else {
//         return <Navigate to="/buyer/dashboard" replace />;
//       }
//     }
//   }

//   // Check if seller needs onboarding for protected seller routes
//   if (isUserAuthenticated && userData?.role === 'seller') {
//     const currentPath = location.pathname;

//     // If seller needs onboarding and is trying to access protected routes
//     if (needsSellerOnboarding(userData) && requiresOnboardingComplete(currentPath)) {
//       return <Navigate to="/seller-onboarding" replace />;
//     }

//     // If seller has completed onboarding but is on onboarding page, redirect to dashboard
//     if (!needsSellerOnboarding(userData) && currentPath === '/seller-onboarding') {
//       return <Navigate to="/seller/dashboard" replace />;
//     }
//   }

//   // Check if the current route is authorized for the user's role
//   useEffect(() => {
//     if (isUserAuthenticated && userData && !isRouteAuthorizedForUser(location.pathname)) {
//       handleUnauthorizedAccess(location.pathname, navigate);
//     }
//   }, [location.pathname, isUserAuthenticated, userData, navigate]);

//   // If all checks pass, render the children
//   return children;
// };

// export default ProtectedRoute;


import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import authService from '../../services/authService';
import { needsSellerOnboarding, requiresOnboardingComplete } from '../../utils/sellerUtils';
import { isRouteAuthorizedForUser, handleUnauthorizedAccess } from '../../utils/navigationUtils';
import { useNavigate } from 'react-router-dom';
/**
* ProtectedRoute component that prevents access based on authentication status
* @param {Object} props - Component props
* @param {React.ReactNode} props.children - Child components to render
* @param {boolean} props.requireAuth - If true, requires authentication to access
* @param {boolean} props.preventAuth - If true, prevents authenticated users from accessing
* @param {string} props.redirectTo - Path to redirect to when access is denied
* @param {string|Array} props.allowedRoles - Roles allowed to access this route
*/
const ProtectedRoute = ({
  children,
  requireAuth = false,
  preventAuth = false,
  redirectTo = '/',
  allowedRoles = null
}) => {
  // ALL HOOKS MUST BE CALLED FIRST - BEFORE ANY CONDITIONAL LOGIC OR EARLY RETURNS
  const { user, isAuthenticated } = useSelector((state) => state.auth);
  const location = useLocation();
  const navigate = useNavigate();
  // Check if user is authenticated (from Redux or localStorage)
  const isUserAuthenticated = isAuthenticated || authService.isAuthenticated();
  const userData = user || authService.getStoredUser();
  // Check if the current route is authorized for the user's role
  useEffect(() => {
    if (isUserAuthenticated && userData && !isRouteAuthorizedForUser(location.pathname)) {
      handleUnauthorizedAccess(location.pathname, navigate);
    }
  }, [location.pathname, isUserAuthenticated, userData, navigate]);
  // NOW WE CAN DO CONDITIONAL LOGIC AND EARLY RETURNS
  // If route prevents authenticated users and user is authenticated, redirect
  if (preventAuth && isUserAuthenticated) {
    // Redirect based on user role and onboarding status
    if (userData?.role === 'admin') {
      return <Navigate to="/admin/dashboard" replace />;
    } else {
      // For non-admin users, use activeRole for redirection
      const effectiveRole = userData?.activeRole || userData?.role;

      if (effectiveRole === 'seller') {
        // For sellers, check onboarding status
        if (needsSellerOnboarding(userData)) {
          return <Navigate to="/seller-onboarding" replace />;
        }
        return <Navigate to="/seller/dashboard" replace />;
      } else {
        return <Navigate to="/buyer/dashboard" replace />;
      }
    }
  }
  // If route requires authentication and user is not authenticated, redirect
  if (requireAuth && !isUserAuthenticated) {
    return <Navigate to="/auth" replace />;
  }
  // If specific roles are required, check user role
  if (allowedRoles && userData) {
    // Use activeRole for non-admin users, role for admin users
    const effectiveRole = userData.role === 'admin' ? userData.role : (userData.activeRole || userData.role);
    const rolesArray = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];

    if (!rolesArray.includes(effectiveRole)) {
      // Redirect to appropriate dashboard based on user's effective role
      if (userData.role === 'admin') {
        return <Navigate to="/admin/dashboard" replace />;
      } else if (effectiveRole === 'seller') {
        return <Navigate to="/seller/dashboard" replace />;
      } else {
        return <Navigate to="/buyer/dashboard" replace />;
      }
    }
  }
  // Check if seller needs onboarding for protected seller routes
  if (isUserAuthenticated && userData?.role === 'seller') {
    const currentPath = location.pathname;
    // If seller needs onboarding and is trying to access protected routes
    if (needsSellerOnboarding(userData) && requiresOnboardingComplete(currentPath)) {
      return <Navigate to="/seller-onboarding" replace />;
    }
    // If seller has completed onboarding but is on onboarding page, redirect to dashboard
    if (!needsSellerOnboarding(userData) && currentPath === '/seller-onboarding') {
      return <Navigate to="/seller/dashboard" replace />;
    }
  }
  // If all checks pass, render the children
  return children;
};
export default ProtectedRoute;