import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { completeSellerOnboarding, reset } from '../../redux/slices/userSlice';
import { getCurrentUser, updateUserOnboardingStatus, uploadProfileImage } from '../../redux/slices/authSlice';
import { showSuccess, showError } from '../../utils/toast';
import SellerOnboardingStep1 from './SellerOnboardingStep1';

import { FaFacebook } from 'react-icons/fa';

import { AiFillInstagram } from "react-icons/ai";
import { FaSquareXTwitter } from "react-icons/fa6";

import './SellerOnboarding.css';

const SellerOnboarding = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { isLoading, isSuccess, isError, error, onboardingData } = useSelector((state) => state.user);

  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    description: '',
    profilePic: '',
    selectedImageFile: null, // Store selected file for deferred upload
    experiences: [{ schoolName: '', position: '', fromYear: '', toYear: '' }],
    minTrainingCost: '',
    socialLinks: {
      facebook: '',
      instagram: '',
      twitter: '',
    },
    sports: [], // Will be populated from user selection
    expertise: [], // Will be populated from user selection
    certifications: [], // Optional field
  });

  // State for inline error messages
  const [fieldErrors, setFieldErrors] = useState({
    description: '',
    experiences: '',
    minTrainingCost: '',
    experienceYears: {}, // Track year validation errors for each experience
    profileImage: '' // Track image validation errors
  });

  // State for server errors
  const [serverError, setServerError] = useState('');

  // State for submission process
  const [submissionState, setSubmissionState] = useState({
    isUploadingImage: false,
    isSubmittingForm: false,
    uploadProgress: ''
  });

  // Disable page interactions during onboarding
  useEffect(() => {
    document.body.style.pointerEvents = 'none';
    const onboardingWrapper = document.querySelector('.seller-onboarding-wrapper');
    if (onboardingWrapper) {
      onboardingWrapper.style.pointerEvents = 'all';
    }

    return () => {
      document.body.style.pointerEvents = 'all';
    };
  }, []);

  // Handle successful completion and errors
  useEffect(() => {
    if (isSuccess) {
      // Reset submission state
      setSubmissionState({
        isUploadingImage: false,
        isSubmittingForm: false,
        uploadProgress: ''
      });

      // Clear any server errors
      setServerError('');

      // Show success toast notification
      showSuccess('Onboarding completed successfully! Welcome to XO Sports Hub!', {
        autoClose: 4000,
      });

      // Update the authSlice user data immediately with the completed onboarding data
      if (onboardingData) {
        dispatch(updateUserOnboardingStatus(onboardingData));
      }

      // Also refresh the user data from server to ensure consistency
      dispatch(getCurrentUser()).then(() => {
        dispatch(reset());
        // Navigate after a short delay to allow toast to be seen
        setTimeout(() => {
          navigate('/seller/dashboard');
        }, 1500);
      });
    }

    if (isError && error) {
      // Reset submission state
      setSubmissionState({
        isUploadingImage: false,
        isSubmittingForm: false,
        uploadProgress: ''
      });

      // Handle server errors
      let errorMessage = 'An error occurred during onboarding. Please try again.';

      if (error.message) {
        errorMessage = error.message;
      } else if (error.errors && Array.isArray(error.errors)) {
        // Handle validation errors from server
        const errorMessages = error.errors.map(err => err.msg).join(', ');
        errorMessage = errorMessages;
      }

      setServerError(errorMessage);
      showError(errorMessage);
    }
  }, [isSuccess, isError, error, dispatch, navigate, onboardingData]);

  const handleInputChange = (field, value) => {
    console.log(`Updating ${field}:`, value);

    setFormData(prev => {
      const updated = {
        ...prev,
        [field]: value
      };
      return updated;
    });

    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Validate experience years
  const validateExperienceYears = () => {
    const currentYear = new Date().getFullYear();
    const minYear = 1950;
    const maxYear = currentYear;
    const yearErrors = {};
    let hasErrors = false;

    formData.experiences.forEach((exp, index) => {
      const fromYear = parseInt(exp.fromYear);
      const toYear = parseInt(exp.toYear);
      const expErrors = {};

      // Validate From Year
      const fromYearStr = String(exp.fromYear || '').trim();
      if (!fromYearStr) {
        expErrors.fromYear = 'From year is required';
        hasErrors = true;
      } else if (isNaN(fromYear) || fromYear < minYear || fromYear > maxYear) {
        expErrors.fromYear = `From year must be between ${minYear} and ${maxYear}`;
        hasErrors = true;
      }

      // Validate To Year
      const toYearStr = String(exp.toYear || '').trim();
      if (!toYearStr) {
        expErrors.toYear = 'To year is required';
        hasErrors = true;
      } else if (isNaN(toYear) || toYear < minYear || toYear > maxYear) {
        expErrors.toYear = `To year must be between ${minYear} and ${maxYear}`;
        hasErrors = true;
      } else if (!isNaN(fromYear) && toYear < fromYear) {
        expErrors.toYear = 'To year must be greater than or equal to from year';
        hasErrors = true;
      }

      if (Object.keys(expErrors).length > 0) {
        yearErrors[index] = expErrors;
      }
    });

    if (hasErrors) {
      setFieldErrors(prev => ({
        ...prev,
        experienceYears: yearErrors
      }));
      return false;
    }

    // Clear year errors if validation passes
    setFieldErrors(prev => ({
      ...prev,
      experienceYears: {}
    }));
    return true;
  };

  // Validate Step 1 fields before proceeding to Step 2
  const validateStep1 = () => {
    const errors = {};
    let hasErrors = false;

    if (!formData.description.trim()) {
      errors.description = 'Description is required';
      hasErrors = true;
    }

    if (formData.experiences.length === 0 || !formData.experiences[0].schoolName) {
      errors.experiences = 'At least one experience with school name is required';
      hasErrors = true;
    }

    // Validate experience years
    const yearValidationPassed = validateExperienceYears();
    if (!yearValidationPassed) {
      hasErrors = true;
    }

    if (hasErrors) {
      setFieldErrors(prev => ({
        ...prev,
        ...errors
      }));
      return false;
    }

    // Clear any existing errors
    setFieldErrors({
      description: '',
      experiences: '',
      minTrainingCost: '',
      experienceYears: {},
      profileImage: ''
    });

    return true;
  };

  // Handle next button click with validation
  const handleNext = () => {
    if (validateStep1()) {
      setStep(2);
    }
  };

  // Monitor profilePic changes
  useEffect(() => {
    console.log('Current formData.profilePic:', formData.profilePic);
  }, [formData.profilePic]);

  const handleSocialChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      socialLinks: {
        ...prev.socialLinks,
        [field]: value
      }
    }));
  };

  const handleExperienceChange = (index, field, value) => {
    const updatedExperiences = [...formData.experiences];
    updatedExperiences[index] = { ...updatedExperiences[index], [field]: value };
    setFormData(prev => ({ ...prev, experiences: updatedExperiences }));

    // Clear year validation errors when user starts typing
    if (field === 'fromYear' || field === 'toYear') {
      if (fieldErrors.experienceYears?.[index]?.[field]) {
        setFieldErrors(prev => ({
          ...prev,
          experienceYears: {
            ...prev.experienceYears,
            [index]: {
              ...prev.experienceYears[index],
              [field]: ''
            }
          }
        }));
      }
    }
  };

  const addExperience = () => {
    setFormData(prev => ({
      ...prev,
      experiences: [...prev.experiences, { schoolName: '', position: '', fromYear: '', toYear: '' }]
    }));
  };

  const removeExperience = (index) => {
    // Only allow removal if there are more than 1 experience
    if (formData.experiences.length > 1) {
      setFormData(prev => ({
        ...prev,
        experiences: prev.experiences.filter((_, idx) => idx !== index)
      }));

      // Clear any validation errors for the removed experience
      if (fieldErrors.experienceYears?.[index]) {
        setFieldErrors(prev => {
          const newExperienceYears = { ...prev.experienceYears };
          delete newExperienceYears[index];

          // Re-index the remaining experience errors
          const reindexedErrors = {};
          Object.keys(newExperienceYears).forEach(key => {
            const keyIndex = parseInt(key);
            if (keyIndex > index) {
              reindexedErrors[keyIndex - 1] = newExperienceYears[key];
            } else if (keyIndex < index) {
              reindexedErrors[keyIndex] = newExperienceYears[key];
            }
          });

          return {
            ...prev,
            experienceYears: reindexedErrors
          };
        });
      }
    }
  };

  const handleSubmit = async () => {
    // Clear previous errors
    setFieldErrors({
      description: '',
      experiences: '',
      minTrainingCost: '',
      experienceYears: {}
    });

    // Clear server errors
    setServerError('');

    // Validate Step 2 required fields (Step 1 is already validated)
    const errors = {};
    let hasErrors = false;

    // Only validate minimum training cost since other fields are validated in Step 1
    if (!formData.minTrainingCost) {
      errors.minTrainingCost = 'Minimum training cost is required';
      hasErrors = true;
    }

    // Double-check Step 1 fields in case user navigated back and modified them
    if (!formData.description.trim()) {
      errors.description = 'Description is required';
      hasErrors = true;
    }

    if (formData.experiences.length === 0 || !formData.experiences[0].schoolName) {
      errors.experiences = 'At least one experience with school name is required';
      hasErrors = true;
    }

    if (hasErrors) {
      setFieldErrors(errors);
      // If Step 1 fields are invalid, go back to Step 1
      if (errors.description || errors.experiences) {
        setStep(1);
      }
      return;
    }

    try {
      let profilePicUrl = formData.profilePic;

      // Step 1: Upload image if one was selected
      if (formData.selectedImageFile) {
        setSubmissionState(prev => ({
          ...prev,
          isUploadingImage: true,
          uploadProgress: 'Uploading profile image...'
        }));

        console.log('Uploading selected image:', formData.selectedImageFile.name);

        try {
          const uploadResult = await dispatch(uploadProfileImage(formData.selectedImageFile)).unwrap();
          profilePicUrl = uploadResult.data.fileUrl;
          console.log('Image uploaded successfully:', profilePicUrl);

          setSubmissionState(prev => ({
            ...prev,
            isUploadingImage: false,
            uploadProgress: 'Image uploaded successfully!'
          }));
        } catch (uploadError) {
          console.error('Image upload failed:', uploadError);
          setSubmissionState(prev => ({
            ...prev,
            isUploadingImage: false,
            uploadProgress: ''
          }));

          // Extract specific error message from upload error
          let errorMessage = 'Failed to upload profile image. Please try again.';
          if (uploadError.message) {
            errorMessage = uploadError.message;
          } else if (uploadError.errors && Array.isArray(uploadError.errors)) {
            errorMessage = uploadError.errors.map(err => err.msg).join(', ');
          }

          // Set field-specific error for image
          setFieldErrors(prev => ({
            ...prev,
            profileImage: errorMessage
          }));

          showError(errorMessage);
          return;
        }
      }

      // Step 2: Submit form data with uploaded image URL
      setSubmissionState(prev => ({
        ...prev,
        isSubmittingForm: true,
        uploadProgress: 'Submitting onboarding data...'
      }));

      // Transform data to match API format - provide default values for required backend fields
      const submitData = {
        description: formData.description,
        profilePic: profilePicUrl,
        experiences: formData.experiences.map(exp => ({
          schoolName: exp.schoolName,
          position: exp.position,
          fromYear: parseInt(exp.fromYear) || new Date().getFullYear(),
          toYear: parseInt(exp.toYear) || new Date().getFullYear()
        })),
        minTrainingCost: parseFloat(formData.minTrainingCost),
        socialLinks: formData.socialLinks,
        sports: formData.sports.length > 0 ? formData.sports : ['General Sports'], // Default if empty
        expertise: formData.expertise.length > 0 ? formData.expertise : ['General Training'], // Default if empty
        certifications: formData.certifications
      };

      console.log('Submitting onboarding data:', submitData);
      dispatch(completeSellerOnboarding(submitData));

    } catch (error) {
      console.error('Submission process failed:', error);
      setSubmissionState(prev => ({
        ...prev,
        isUploadingImage: false,
        isSubmittingForm: false,
        uploadProgress: ''
      }));

      showError('An error occurred during submission. Please try again.');
    }
  };

  return (
    <div className="seller-onboarding-wrapper max-container">
      {step === 1 ? (
        <SellerOnboardingStep1
          formData={formData}
          onInputChange={handleInputChange}
          onExperienceChange={handleExperienceChange}
          onAddExperience={addExperience}
          onRemoveExperience={removeExperience}
          onNext={handleNext}
          fieldErrors={fieldErrors}
        />
      ) : (
        <div className="seller-onboarding-step2-container">
          {/* Stepper */}
          <div className="progress-bar">
            <div className="step complete">1</div>
            <div className="progress-line" />
            <div className="step active">2</div>
          </div>

          {/* Minimum Customer Training Cost */}
          <div className="section-block">
            <div className="section-title">Minimum Customer Training Cost</div>
            <input
              type="number"
              className={`input min-cost-input ${fieldErrors.minTrainingCost ? 'error' : ''}`}
              placeholder="Enter amount"
              value={formData.minTrainingCost}
              onChange={e => handleInputChange('minTrainingCost', e.target.value)}
            />
            {fieldErrors.minTrainingCost && (
              <div className="field-error">{fieldErrors.minTrainingCost}</div>
            )}
          </div>

          {/* Social Media Account */}
          <div className="section-block">
            <div className="section-title">Social Media Account</div>
            <div className="social-inputs-grid">
              <div className="social-input-row">
                <span className="social-icon facebook">
                  <FaFacebook/>
                </span>
                <input
                  type="text"
                  className="input social-input"
                  placeholder="Facebook URL"
                  value={formData.socialLinks.facebook}
                  onChange={e => handleSocialChange('facebook', e.target.value)}
                />
              </div>
              <div className="social-input-row">
                <span className="social-icon linkedin">
                  <AiFillInstagram/></span>
                <input
                  type="text"
                  className="input social-input"
                  placeholder="Instagram URL"
                  value={formData.socialLinks.instagram}
                  onChange={e => handleSocialChange('instagram', e.target.value)}
                />
              </div>
              <div className="social-input-row">
                <span className="social-icon twitter">
                  <FaSquareXTwitter />
                </span>
                <input
                  type="text"
                  className="input social-input"
                  placeholder="Twitter URL"
                  value={formData.socialLinks.twitter}
                  onChange={e => handleSocialChange('twitter', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Server Error Display */}
          {serverError && (
            <div className="server-error-message">
              <div className="error-icon">⚠️</div>
              <div className="error-text">{serverError}</div>
            </div>
          )}

          {/* Navigation Buttons */}
          <div className="next-btn-row">
            <button
              className="btn btn-outline"
              onClick={() => setStep(1)}
              disabled={submissionState.isUploadingImage || submissionState.isSubmittingForm || isLoading}
            >
              Back
            </button>
            <button
              className="btn btn-primary next-btn"
              onClick={handleSubmit}
              disabled={submissionState.isUploadingImage || submissionState.isSubmittingForm || isLoading}
            >
              {submissionState.isUploadingImage || submissionState.isSubmittingForm || isLoading
                ? (submissionState.uploadProgress || 'Processing...')
                : 'Complete Onboarding'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SellerOnboarding;