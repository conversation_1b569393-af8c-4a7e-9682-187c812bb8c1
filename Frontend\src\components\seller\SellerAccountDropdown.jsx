import React, { useState, useRef, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { setActiveTab } from "../../redux/slices/sellerDashboardSlice";
import { logout, getUserForNavbar } from "../../redux/slices/authSlice";
import { getImageUrl } from "../../utils/constants";
import "../../styles/AccountDropdown.css";

// Icons
import { MdDashboard, MdKeyboardArrowDown } from "react-icons/md";
import { FaUser } from "react-icons/fa";
import { MdRequestPage } from "react-icons/md";
import { FaGavel } from "react-icons/fa";
import { MdVideoLibrary } from "react-icons/md";
import { FaCreditCard } from "react-icons/fa";
import { IoLogOut } from "react-icons/io5";

const SellerAccountDropdown = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [userData, setUserData] = useState(null);
  const [imageError, setImageError] = useState(false);
  const dropdownRef = useRef(null);
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const path = location.pathname;

  // Get auth state from Redux
  const { user, isAuthenticated } = useSelector((state) => state.auth);

  // Fetch user data on component mount if authenticated
  useEffect(() => {
    const token = localStorage.getItem("xosportshub_token");
    if (token && !user) {
      dispatch(getUserForNavbar());
    }
  }, [dispatch, user]);

  // Update userData when user changes
  useEffect(() => {
    if (user) {
      setUserData(user);
    }
  }, [user]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle image error
  const handleImageError = () => {
    setImageError(true);
  };

  // Check if a tab is active
  const isActive = (tab) => {
    const tabRoutes = {
      dashboard: "/seller/dashboard",
      profile: "/seller/profile",
      "my-sports-strategies": "/seller/my-sports-strategies",
      requests: "/seller/requests",
      bids: "/seller/bids",
      cards: "/seller/cards",
    };
    return path === tabRoutes[tab];
  };

  // Handle navigation and set active tab
  const handleNavigation = (tab) => {
    setIsOpen(false);
    dispatch(setActiveTab(tab));

    // Navigate to the corresponding route
    switch (tab) {
      case "dashboard":
        navigate("/seller/dashboard");
        break;
      case "profile":
        navigate("/seller/profile");
        break;
      case "my-sports-strategies":
        navigate("/seller/my-sports-strategies");
        break;
      case "requests":
        navigate("/seller/requests");
        break;
      case "bids":
        navigate("/seller/bids");
        break;
      case "cards":
        navigate("/seller/cards");
        break;
      case "logout":
        // Handle logout logic here
        dispatch(logout());
        navigate("/");
        break;
      default:
        navigate("/seller/dashboard");
    }
  };

  // Don't render if not authenticated
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="account-dropdown" ref={dropdownRef}>
      <button
        className={`account-dropdown__button ${isOpen ? "active" : ""}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        {(userData?.sellerInfo?.profilePic || userData?.profileImage) && !imageError ? (
          <img
            src={getImageUrl(userData?.sellerInfo?.profilePic || userData?.profileImage)}
            alt="Profile"
            className="account-dropdown__user-image"
            onError={handleImageError}
          />
        ) : (
          <div className="account-dropdown__user-avatar">
            {userData && userData.firstName && userData.lastName ? (
              `${userData.firstName.charAt(0)}${userData.lastName.charAt(0)}`
            ) : (
              <FaUser className="account-dropdown__user-icon" />
            )}
          </div>
        )}
        <div className="account-dropdown__user-info">
          <span className="account-dropdown__user-name">
            {userData
              ? `${userData.firstName} ${userData.lastName}`
              : "My Account"}
          </span>
        </div>
        <MdKeyboardArrowDown className="account-dropdown__icon" />
      </button>

      <div className={`account-dropdown__menu ${isOpen ? "active" : ""}`}>
        <Link
          to="/seller/dashboard"
          className={`account-dropdown__item ${
            isActive("dashboard") ? "active" : ""
          }`}
          onClick={() => handleNavigation("dashboard")}
        >
          <MdDashboard className="account-dropdown__item-icon" />
          Dashboard
        </Link>

        

        <Link
          to="/seller/my-sports-strategies"
          className={`account-dropdown__item ${
            isActive("my-sports-strategies") ? "active" : ""
          }`}
          onClick={() => handleNavigation("my-sports-strategies")}
        >
          <MdVideoLibrary className="account-dropdown__item-icon" />
          My Sports Strategies
        </Link>

        <Link
          to="/seller/requests"
          className={`account-dropdown__item ${
            isActive("requests") ? "active" : ""
          }`}
          onClick={() => handleNavigation("requests")}
        >
          <MdRequestPage className="account-dropdown__item-icon" />
          Requests
        </Link>

        <Link
          to="/seller/bids"
          className={`account-dropdown__item ${
            isActive("bids") ? "active" : ""
          }`}
          onClick={() => handleNavigation("bids")}
        >
          <FaGavel className="account-dropdown__item-icon" />
          My Bids
        </Link>

        <Link
          to="/seller/cards"
          className={`account-dropdown__item ${
            isActive("cards") ? "active" : ""
          }`}
          onClick={() => handleNavigation("cards")}
        >
          <FaCreditCard className="account-dropdown__item-icon" />
          My Cards
        </Link>
        <Link
          to="/seller/profile"
          className={`account-dropdown__item ${
            isActive("profile") ? "active" : ""
          }`}
          onClick={() => handleNavigation("profile")}
        >
          <FaUser className="account-dropdown__item-icon" />
          My Profile
        </Link>

        <div className="account-dropdown__divider"></div>

        <Link
          to="/"
          className="account-dropdown__item"
          onClick={() => handleNavigation("logout")}
        >
          <IoLogOut className="account-dropdown__item-icon" />
          Logout
        </Link>
      </div>
    </div>
  );
};

export default SellerAccountDropdown;
