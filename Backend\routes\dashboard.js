const express = require('express');
const {
  getDashboardStats,
  getUserStats,
  getContentStats,
  getOrderStats,
  getRevenueStats,
  getRecentActivity,
  getBuyerDashboardStats
} = require('../controllers/dashboard');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Protected routes
router.use(protect);

// Buyer-specific dashboard stats (accessible to buyers, sellers, and admins)
router.get('/stats/buyer', authorize('buyer', 'seller', 'admin'), getBuyerDashboardStats);

// Admin-only dashboard routes
router.use(authorize('admin'));

// Main dashboard stats
router.get('/stats', getDashboardStats);

// Individual stat endpoints for more detailed data
router.get('/users', getUserStats);
router.get('/content', getContentStats);
router.get('/orders', getOrderStats);
router.get('/revenue', getRevenueStats);
router.get('/activity', getRecentActivity);

module.exports = router;
