# Role Toggle Feature Implementation

## Overview
This document describes the implementation of the role toggle feature that allows users to switch between buyer and seller roles dynamically while maintaining admin role separation.

## Features Implemented

### 1. Backend Changes

#### User Model Updates (`Backend/models/User.js`)
- Added `activeRole` field to store the currently active role for non-admin users
- Added helper methods:
  - `canToggleRoles()` - Check if user can toggle roles (non-admin only)
  - `toggleActiveRole()` - Switch between buyer and seller roles
  - `getEffectiveRole()` - Get the role to use for authorization

#### TempUser Model Updates (`Backend/models/TempUser.js`)
- Added `activeRole` field for consistency during registration

#### Auth Controller Updates (`Backend/controllers/auth.js`)
- Added `toggleRole` endpoint for switching roles
- Updated `sendTokenResponse` to include `activeRole` in user data
- Role toggle validates that only non-admin users can switch roles

#### Auth Routes (`Backend/routes/auth.js`)
- Added `POST /api/auth/toggle-role` route (protected)

#### Middleware Updates (`Backend/middleware/auth.js`)
- Updated `authorize` middleware to use effective role (activeRole for non-admin, role for admin)

### 2. Frontend Changes

#### Redux State Management
- Updated `authSlice.js` to handle role toggle actions
- Added `toggleRole` async thunk for API calls
- Updated reducers to handle role toggle success/failure

#### Services
- Added `toggleRole` function to `authService.js`
- Updated constants to include `TOGGLE_ROLE` endpoint

#### Components

##### RoleToggle Component (`Frontend/src/components/common/RoleToggle.jsx`)
- Interactive toggle button for switching between buyer/seller modes
- Shows current active role
- Handles role switching with loading states
- Navigates to appropriate dashboard after role switch
- Responsive design with mobile support

##### Navigation Updates
- Updated `Navbar.jsx` to use effective role for navigation
- Added role toggle component to navbar for authenticated non-admin users
- Updated `Sidebar.jsx` to include role toggle in mobile view
- Modified navigation logic to support dual roles

##### Route Protection
- Updated `ProtectedRoute.jsx` to use activeRole for authorization
- Maintains admin role separation
- Redirects based on effective role

#### Utilities
- Updated `navigationUtils.js` to handle effective roles
- Added `getEffectiveUserRole()` function
- Modified navigation handlers to support role switching

#### Styling
- Created `RoleToggle.css` with responsive design
- Added sidebar role toggle styles
- Mobile-friendly toggle interface

## API Endpoints

### POST /api/auth/toggle-role
**Description:** Toggle user role between buyer and seller  
**Authentication:** Required  
**Authorization:** Non-admin users only  

**Response:**
```json
{
  "success": true,
  "message": "Role switched to seller",
  "data": {
    "_id": "user_id",
    "firstName": "User",
    "lastName": "Name",
    "role": "buyer",
    "activeRole": "seller",
    ...
  }
}
```

## User Experience

### For Regular Users (Non-Admin)
1. **Role Toggle Button:** Visible in navbar and mobile sidebar
2. **Seamless Switching:** Click toggle to switch between buyer/seller modes
3. **Visual Feedback:** Button shows current mode and loading state
4. **Auto Navigation:** Automatically redirects to appropriate dashboard
5. **Persistent State:** Role preference maintained across sessions

### For Admin Users
- No role toggle button (admins have access to all features)
- Existing admin functionality unchanged
- Can access both buyer and seller routes without restrictions

## Technical Details

### Database Schema
```javascript
// User Model
{
  role: {
    type: String,
    enum: ["buyer", "seller", "admin"],
    default: "buyer"
  },
  activeRole: {
    type: String,
    enum: ["buyer", "seller"],
    default: function() {
      return this.role === "admin" ? undefined : this.role;
    }
  }
}
```

### Authorization Logic
- **Admin users:** Use `role` field for all authorization
- **Non-admin users:** Use `activeRole` field for authorization
- **Route protection:** Based on effective role
- **API access:** Controlled by effective role

## Testing

### Backend API Testing
✅ User registration with role assignment  
✅ Role toggle endpoint functionality  
✅ Authorization middleware with effective roles  
✅ Admin role separation maintained  

### Frontend Integration
✅ Role toggle component rendering  
✅ Redux state management  
✅ Navigation updates based on active role  
✅ Route protection with effective roles  

## Security Considerations

1. **Admin Protection:** Admin users cannot toggle roles
2. **Authentication Required:** Role toggle requires valid JWT token
3. **Authorization Checks:** All protected routes use effective role
4. **State Consistency:** Frontend and backend state synchronized
5. **Session Management:** Role changes persist across browser sessions

## Future Enhancements

1. **Role Permissions:** Fine-grained permissions within roles
2. **Role History:** Track role switching history
3. **Default Role:** Allow users to set preferred default role
4. **Role-Specific Onboarding:** Different onboarding flows per role
5. **Analytics:** Track role usage patterns

## Migration Notes

- Existing users will have `activeRole` set to match their current `role`
- No breaking changes to existing API endpoints
- Backward compatible with existing frontend code
- Admin functionality remains unchanged
