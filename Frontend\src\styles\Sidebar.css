.sidebar-component {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: var(--z-index-modal);
  pointer-events: none;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0s linear 0.3s, opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.sidebar-component.active {
  pointer-events: auto;
  visibility: visible;
  opacity: 1;
  transition: visibility 0s, opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.sidebar-component .sidebar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: opacity;
}

.sidebar-component.active .sidebar-overlay {
  opacity: 1;
}

.sidebar-component .sidebar {
  position: absolute;
  top: 0;
  left: 0;
  width: 60%;
  height: 100%;
  background-color: var(--white);
  box-shadow: var(--box-shadow);
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  display: flex;
  flex-direction: column;
  padding: 20px;
  will-change: transform;
}

.sidebar-component.active .sidebar {
  transform: translateX(0);
}

.sidebar-component .sidebar-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--light-gray);
}

.sidebar-component .sidebar-logo {
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-component .sidebar-logo img {
  height: 50px;
  width: auto;
  max-width: 120px;
  transition: opacity 0.3s ease;
}

.sidebar-component .sidebar-logo a:hover img {
  opacity: 0.8;
}

.sidebar-component .sidebar-links {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.sidebar-component .sidebar-links a {
  text-decoration: none;
  color: var(--secondary-color);
  font-weight: 500;
  font-size: var(--basefont);
  transition: color 0.3s ease;
  padding: 5px 0;
  display: flex;
  align-items: center;
}

.sidebar-component .sidebar-icon {
  margin-right: 10px;
  font-size: var(--basefont);
}

.sidebar-component .sidebar-links a:hover {
  color: var(--btn-color);
}

.sidebar-component .sidebar-links a.active {
  color: var(--btn-color);
  font-weight: 600;
}

.sidebar-component .sidebar-auth {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 20px;

}

.sidebar-component .sidebar-auth .btn {
  display: flex;
  padding: 8px 16px;
  border-radius: var(--border-radius);
  text-align: center;
  font-weight: 500;
  transition: all 0.3s ease;
}

.sidebar-component .sidebar-auth .signinbtn {
  background-color: transparent;
  color: var(--btn-color);
  border: 1px solid var(--btn-color);
}

.sidebar-component .sidebar-auth .signinbtn:hover {
  background-color: var(--btn-color);
  color: var(--white);
}

.sidebar-component .sidebar-auth .signupbtn {
  background-color: var(--btn-color);
  color: var(--white);
  border: 1px solid var(--btn-color);
}

.sidebar-component .sidebar-auth .signupbtn:hover {
  background-color: var(--btn-color);
  border-color: var(--btn-color);
}

.sidebar-component .sidebar-auth .btn-outline {
  background-color: transparent;
  color: var(--btn-color);
  border: 1px solid var(--btn-color);
}

.sidebar-component .sidebar-auth .btn-outline:hover {
  background-color: var(--btn-color);
  color: var(--white);
}

/* Account dropdown styles */
.sidebar-account-section {
  margin-top: 10px;
 
  padding-top: 10px;
}

.sidebar-account-header {
 display: flex
;
    justify-content: space-between;
    align-items: center;
    padding: 10px 5px;
    cursor: pointer;
    color: var(--secondary-color);
    font-weight: 500;
    border: 1px solid var(--light-gray);
    border-radius: var(--border-radius);
}

.sidebar-account-header:hover {
  color: var(--btn-color);
}

.sidebar-dropdown-icon {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.sidebar-dropdown-icon.active {
  transform: rotate(180deg);
}

.sidebar-account-dropdown {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 10px 0 10px 15px;
  margin-top: 5px;
  border-left: 2px solid var(--light-gray);
}

.sidebar-account-dropdown a {
  padding: 5px 0;
}

/* Role toggle in sidebar */
.sidebar-role-toggle {
  padding: 15px 0;
  border-bottom: 1px solid var(--light-gray);
  margin-bottom: 15px;
}

.sidebar-role-toggle .role-toggle {
  margin: 0;
  justify-content: center;
}

.sidebar-role-toggle .role-toggle__container {
  flex-direction: column;
  gap: 0.5rem;
  align-items: center;
}

.sidebar-role-toggle .role-toggle__label {
  font-size: 0.8rem;
  text-align: center;
}

.sidebar-role-toggle .role-toggle__switch {
  font-size: 0.8rem;
  padding: 0.4rem 0.8rem;
}

/* Only show sidebar on small screens */
@media (min-width: 769px) {
  .sidebar-component {
    display: none !important;
  }
}
