const express = require("express");
const { check } = require("express-validator");
const {
  getAllContent,
  getContent,
  createContent,
  updateContent,
  deleteContent,
  getSellerContent,
  getSellerContentById,
  getContentCategories,
  getTrendingContent,
  toggleContentStatus,
  getContentAccess,
  getContentPreview,
  testPreviewGeneration,
} = require("../controllers/content");

const { protect, authorize } = require("../middleware/auth");
const upload = require("../utils/fileUpload");

const router = express.Router();

// Public routes
router.get("/", getAllContent);
router.get("/categories", getContentCategories);
router.get("/trending", getTrendingContent);
router.get("/:id/preview", getContentPreview);
router.get("/:id", getContent);

// Protected routes
router.use(protect);

// Secure content access routes
router.get("/:id/access", getContentAccess);

// Seller routes
router.get("/seller/me", authorize("seller"), getSellerContent);
router.get("/seller/:id", authorize("seller"), getSellerContentById);

router.post(
  "/",
  authorize("seller"),
  [
    // Required fields that are visible in frontend
    check("title", "Title is required").not().isEmpty(),
    check("description", "Description is required").not().isEmpty(),
    check("sport", "Sport is required").not().isEmpty(),
    check("contentType", "Content type is required").not().isEmpty(),
    check("fileUrl", "File URL is required").not().isEmpty(),
    check("category", "Category is required").not().isEmpty(),
    check("difficulty", "Difficulty level is required").not().isEmpty(),
    check("aboutCoach", "About coach information is required").not().isEmpty(),
    check("strategicContent", "Strategic content description is required")
      .not()
      .isEmpty(),
    check("saleType", "Sale type is required").not().isEmpty(),

    // Optional fields that are used in frontend with default values
    check("language", "Language is required")
      .optional()
      .isIn([
        "English",
        "Spanish",
        "French",
        "German",
        "Italian",
        "Portuguese",
        "Chinese",
        "Japanese",
        "Korean",
        "Other",
      ]),

    // COMMENTED OUT - Fields not used in current frontend UI
    // check('videoLength', 'Video length must be a positive number').optional().isFloat({ min: 0 }),
    // check('prerequisites', 'Prerequisites must be an array').optional().isArray(),
    // check('learningObjectives', 'Learning objectives must be an array').optional().isArray(),
    // check('equipment', 'Equipment must be an array').optional().isArray()
  ],
  createContent
);

router
  .route("/:id")
  .put(authorize("seller", "admin"), updateContent)
  .delete(authorize("seller", "admin"), deleteContent);

// Toggle content status route
router.put(
  "/:id/toggle-status",
  authorize("seller", "admin"),
  toggleContentStatus
);

// File upload route
router.post(
  "/upload",
  protect,
  authorize("seller"),
  upload.single("file"),
  async (req, res, next) => {
    try {
      const fileUrl = req.file.location || `/uploads/${req.file.filename}`;

      res.status(200).json({
        success: true,
        data: {
          fileUrl: fileUrl,
          fileName: req.file.originalname,
          fileType: req.file.mimetype,
          fileSize: req.file.size,
        },
        message: "File uploaded successfully. Preview will be generated when content is created."
      });
    } catch (error) {
      next(error);
    }
  }
);

// Test preview generation route (development only)
if (process.env.NODE_ENV !== 'production') {
  router.post(
    "/test-preview",
    protect,
    authorize("seller", "admin"),
    testPreviewGeneration
  );
}

module.exports = router;
