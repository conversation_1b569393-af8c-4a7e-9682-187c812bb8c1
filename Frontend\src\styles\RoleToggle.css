.role-toggle {
  display: flex;
  align-items: center;
  margin: 0 1rem;
}

.role-toggle__container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.role-toggle__label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary, #333);
  white-space: nowrap;
}

.role-toggle__switch {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  border: 2px solid var(--primary-color, #007bff);
  border-radius: 25px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--primary-color, #007bff);
  position: relative;
  overflow: hidden;
}

.role-toggle__switch:hover {
  background: var(--primary-color, #007bff);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.role-toggle__switch:active {
  transform: translateY(0);
}

.role-toggle__switch:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.role-toggle__switch.buyer {
  border-color: var(--buyer-color, #28a745);
  color: var(--buyer-color, #28a745);
}

.role-toggle__switch.buyer:hover {
  background: var(--buyer-color, #28a745);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.role-toggle__switch.seller {
  border-color: var(--seller-color, #fd7e14);
  color: var(--seller-color, #fd7e14);
}

.role-toggle__switch.seller:hover {
  background: var(--seller-color, #fd7e14);
  box-shadow: 0 4px 12px rgba(253, 126, 20, 0.3);
}

.role-toggle__slider {
  position: relative;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: currentColor;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.role-toggle__thumb {
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
  line-height: 1;
}

.role-toggle__text {
  white-space: nowrap;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .role-toggle {
    margin: 0 0.5rem;
  }
  
  .role-toggle__container {
    gap: 0.5rem;
  }
  
  .role-toggle__label {
    font-size: 0.8rem;
  }
  
  .role-toggle__switch {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }
  
  .role-toggle__slider {
    width: 20px;
    height: 20px;
  }
  
  .role-toggle__thumb {
    font-size: 0.7rem;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .role-toggle__text {
    display: none;
  }
  
  .role-toggle__switch {
    padding: 0.4rem;
    min-width: 40px;
    justify-content: center;
  }
}

/* Animation for switching */
.role-toggle__switch.switching {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .role-toggle__label {
    color: var(--text-primary-dark, #f8f9fa);
  }
}
